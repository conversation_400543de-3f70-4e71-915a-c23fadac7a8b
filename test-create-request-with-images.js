const fetch = require('node-fetch');

async function testCreateRequestWithImages() {
  try {
    console.log('🧪 Testing Create Request with Images...');
    
    // Test data with images
    const requestData = {
      title: 'Test Request with Images',
      description: 'This is a test request with image attachments for validation testing',
      serviceType: 'wound_care',
      coordinates: [31.233, 30.033],
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      estimatedDuration: 2,
      urgencyLevel: 'medium',
      budget: 150,
      contactPhone: '01234567890',
      notes: 'Test notes',
      images: [
        {
          filename: 'test-image-1.jpg',
          originalName: 'medical-report.jpg',
          url: '/api/uploads/images/test-image-1.jpg',
          size: 1024000
        },
        {
          filename: 'test-image-2.png',
          originalName: 'condition-photo.png',
          url: '/api/uploads/images/test-image-2.png',
          size: 2048000
        }
      ]
    };
    
    console.log('📡 Testing create request with images...');
    console.log('🔍 Request Data:', JSON.stringify(requestData, null, 2));
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need a real token
      },
      body: JSON.stringify(requestData)
    });
    
    console.log('📊 Response Status:', response.status);
    console.log('📊 Response Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Request created successfully:', responseData);
    } else {
      const errorText = await response.text();
      console.error('❌ Request creation error:', errorText);
      
      // Try to parse as JSON if possible
      try {
        const errorJson = JSON.parse(errorText);
        console.error('❌ Error details:', errorJson);
      } catch (e) {
        console.error('❌ Raw error text:', errorText);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('❌ Full error:', error);
  }
}

// Test with empty images array
async function testCreateRequestWithEmptyImages() {
  try {
    console.log('\n🧪 Testing Create Request with Empty Images Array...');
    
    const requestData = {
      title: 'Test Request with Empty Images',
      description: 'This is a test request with empty images array',
      serviceType: 'wound_care',
      coordinates: [31.233, 30.033],
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      estimatedDuration: 2,
      urgencyLevel: 'medium',
      images: [] // Empty array
    };
    
    console.log('📡 Testing create request with empty images...');
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(requestData)
    });
    
    console.log('📊 Response Status:', response.status);
    
    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Request with empty images created successfully');
    } else {
      const errorText = await response.text();
      console.error('❌ Request with empty images failed:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Empty images test failed:', error.message);
  }
}

// Test without images field
async function testCreateRequestWithoutImages() {
  try {
    console.log('\n🧪 Testing Create Request without Images Field...');
    
    const requestData = {
      title: 'Test Request without Images Field',
      description: 'This is a test request without images field at all',
      serviceType: 'wound_care',
      coordinates: [31.233, 30.033],
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      estimatedDuration: 2,
      urgencyLevel: 'medium'
      // No images field at all
    };
    
    console.log('📡 Testing create request without images field...');
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(requestData)
    });
    
    console.log('📊 Response Status:', response.status);
    
    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Request without images field created successfully');
    } else {
      const errorText = await response.text();
      console.error('❌ Request without images field failed:', errorText);
    }
    
  } catch (error) {
    console.error('❌ No images field test failed:', error.message);
  }
}

// Run all tests
async function runTests() {
  await testCreateRequestWithImages();
  console.log('\n' + '='.repeat(50));
  await testCreateRequestWithEmptyImages();
  console.log('\n' + '='.repeat(50));
  await testCreateRequestWithoutImages();
}

runTests();
