# Database Configuration
MONGODB_URI=mongodb://localhost:27017/nurse-platform

# OpenAI API Configuration
OPENAI_API_KEY=sk-proj-YOUR_OPENAI_API_KEY_HERE

# Stripe Configuration (Sandbox/Test Mode)
STRIPE_SECRET_KEY=sk_test_YOUR_STRIPE_SECRET_KEY_HERE
STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_STRIPE_PUBLISHABLE_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET_HERE
STRIPE_CURRENCY=egp

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here

# Email Configuration (Optional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
# FROM_EMAIL=<EMAIL>

# Application Configuration
NODE_ENV=development
PORT=3001

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads
