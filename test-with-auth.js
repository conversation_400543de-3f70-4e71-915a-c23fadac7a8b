const fetch = require('node-fetch');

let authToken = null;

async function loginAndGetToken() {
  try {
    console.log('🔐 Logging in to get auth token...');
    
    // Try to login with test credentials
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(loginData)
    });
    
    if (response.ok) {
      const data = await response.json();
      authToken = data.access_token || data.token;
      console.log('✅ Login successful, got token:', authToken ? 'YES' : 'NO');
      return authToken;
    } else {
      const errorText = await response.text();
      console.log('❌ Login failed:', errorText);
      
      // Try to register a test user first
      console.log('🔄 Trying to register test user...');
      return await registerTestUser();
    }
  } catch (error) {
    console.error('❌ Login error:', error.message);
    return null;
  }
}

async function registerTestUser() {
  try {
    const registerData = {
      name: 'Test Patient',
      email: '<EMAIL>',
      password: 'password123',
      role: 'patient',
      phone: '01234567890',
      address: '123 Test Street, Cairo, Egypt'
    };
    
    const response = await fetch('http://localhost:3001/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registerData)
    });
    
    if (response.ok) {
      const data = await response.json();
      authToken = data.access_token || data.token;
      console.log('✅ Registration successful, got token:', authToken ? 'YES' : 'NO');
      return authToken;
    } else {
      const errorText = await response.text();
      console.log('❌ Registration failed:', errorText);
      return null;
    }
  } catch (error) {
    console.error('❌ Registration error:', error.message);
    return null;
  }
}

async function testWithValidAuth() {
  try {
    // First get a valid token
    const token = await loginAndGetToken();
    
    if (!token) {
      console.error('❌ Could not get valid auth token, skipping tests');
      return;
    }
    
    console.log('\n🧪 Testing with Valid Authentication...');
    
    // Test the debug endpoint
    console.log('📡 Testing debug endpoint...');
    
    const debugData = {
      title: 'Test Request with Valid Auth',
      description: 'This is a test request with valid authentication token',
      serviceType: 'wound_care',
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      estimatedDuration: 2,
      urgencyLevel: 'medium',
      coordinates: [31.233, 30.033],
      images: [
        {
          filename: 'test-image.jpg',
          originalName: 'photo.jpg',
          url: '/api/uploads/images/test-image.jpg',
          size: 1024000
        }
      ]
    };
    
    const debugResponse = await fetch('http://localhost:3001/api/requests/debug', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(debugData)
    });
    
    console.log('📊 Debug Response Status:', debugResponse.status);
    
    if (debugResponse.ok) {
      const debugResult = await debugResponse.json();
      console.log('✅ Debug endpoint successful');
    } else {
      const debugError = await debugResponse.text();
      console.error('❌ Debug endpoint failed:', debugError);
    }
    
    // Test the real endpoint
    console.log('\n📡 Testing real create request endpoint...');
    
    const realResponse = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(debugData)
    });
    
    console.log('📊 Real Response Status:', realResponse.status);
    
    if (realResponse.ok) {
      const realResult = await realResponse.json();
      console.log('✅ Real endpoint successful:', realResult);
    } else {
      const realError = await realResponse.text();
      console.error('❌ Real endpoint failed:', realError);
      
      // Try to parse the error for more details
      try {
        const errorJson = JSON.parse(realError);
        console.error('❌ Detailed error:', errorJson);
      } catch (e) {
        console.error('❌ Could not parse error as JSON');
      }
    }
    
  } catch (error) {
    console.error('❌ Test with auth failed:', error.message);
  }
}

async function testMinimalRequest() {
  try {
    if (!authToken) {
      console.log('❌ No auth token available');
      return;
    }
    
    console.log('\n🧪 Testing Minimal Request...');
    
    // Absolute minimal data
    const minimalData = {
      title: 'Minimal Test Request',
      description: 'This is a minimal test description that meets requirements',
      serviceType: 'wound_care',
      coordinates: [31.233, 30.033],
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
    
    console.log('📡 Testing minimal request...');
    console.log('🔍 Minimal data:', JSON.stringify(minimalData, null, 2));
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(minimalData)
    });
    
    console.log('📊 Minimal Response Status:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Minimal request successful');
    } else {
      const error = await response.text();
      console.error('❌ Minimal request failed:', error);
    }
    
  } catch (error) {
    console.error('❌ Minimal test failed:', error.message);
  }
}

async function testWithEmptyImages() {
  try {
    if (!authToken) {
      console.log('❌ No auth token available');
      return;
    }
    
    console.log('\n🧪 Testing with Empty Images...');
    
    const dataWithEmptyImages = {
      title: 'Test Request with Empty Images',
      description: 'This is a test request with empty images array',
      serviceType: 'wound_care',
      coordinates: [31.233, 30.033],
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      images: []
    };
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(dataWithEmptyImages)
    });
    
    console.log('📊 Empty Images Response Status:', response.status);
    
    if (response.ok) {
      console.log('✅ Empty images request successful');
    } else {
      const error = await response.text();
      console.error('❌ Empty images request failed:', error);
    }
    
  } catch (error) {
    console.error('❌ Empty images test failed:', error.message);
  }
}

// Run all tests
async function runTests() {
  await testWithValidAuth();
  await testMinimalRequest();
  await testWithEmptyImages();
}

runTests();
