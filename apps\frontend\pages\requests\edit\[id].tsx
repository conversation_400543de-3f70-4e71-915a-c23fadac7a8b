import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../../lib/auth';
import Layout, { Card } from '../../../components/Layout';
import { apiService } from '../../../lib/api';
import ImageUpload from '../../../components/ImageUpload';

interface RequestData {
  id: string;
  title: string;
  description: string;
  serviceType: string;
  address: string;
  coordinates: [number, number];
  scheduledDate: string;
  estimatedDuration: number;
  urgencyLevel: string;
  specialRequirements: string;
  budget: number;
  contactPhone: string;
  notes: string;
  status: string;
  images?: Array<{
    filename: string;
    originalName: string;
    url: string;
    size: number;
  }>;
}

export default function EditRequest() {
  const router = useRouter();
  const { id } = router.query;
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [originalRequest, setOriginalRequest] = useState<RequestData | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    serviceType: '',
    address: '',
    scheduledDate: '',
    estimatedDuration: '',
    urgencyLevel: 'medium',
    specialRequirements: '',
    budget: '',
    contactPhone: '',
    notes: '',
    images: [] as Array<{
      filename: string;
      originalName: string;
      url: string;
      size: number;
    }>,
  });

  useEffect(() => {
    if (id && !authLoading) {
      loadRequest();
    }
  }, [id, authLoading]);

  const loadRequest = async () => {
    try {
      setLoading(true);
      const response = await apiService.getRequestById(id as string);
      const requestData = response?.data || response;
      
      if (requestData.status !== 'pending') {
        setError('Only pending requests can be edited');
        return;
      }

      setOriginalRequest(requestData);
      
      // Populate form with existing data
      setFormData({
        title: requestData.title || '',
        description: requestData.description || '',
        serviceType: requestData.serviceType || '',
        address: requestData.address || '',
        scheduledDate: requestData.scheduledDate ? new Date(requestData.scheduledDate).toISOString().slice(0, 16) : '',
        estimatedDuration: requestData.estimatedDuration?.toString() || '',
        urgencyLevel: requestData.urgencyLevel || 'medium',
        specialRequirements: requestData.specialRequirements || '',
        budget: requestData.budget?.toString() || '',
        contactPhone: requestData.contactPhone || '',
        notes: requestData.notes || '',
        images: requestData.images || [],
      });
    } catch (err: any) {
      console.error('❌ Error loading request:', err);
      setError(err.message || 'Failed to load request details');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImagesChange = (images: Array<{
    filename: string;
    originalName: string;
    url: string;
    size: number;
  }>) => {
    setFormData(prev => ({ ...prev, images }));
  };

  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.title.trim()) errors.push('Request title is required');
    if (formData.title.length < 5) errors.push('Title must be at least 5 characters');
    if (!formData.description.trim()) errors.push('Description is required');
    if (formData.description.length < 10) errors.push('Description must be at least 10 characters');
    if (!formData.serviceType) errors.push('Service type is required');
    if (!formData.address.trim()) errors.push('Address is required');
    if (!formData.scheduledDate) errors.push('Scheduled date is required');
    if (!formData.estimatedDuration) errors.push('Estimated duration is required');

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors.join(', '));
      return;
    }

    try {
      setSaving(true);
      setError('');

      // Prepare update data (only include changed fields)
      const updateData: any = {};
      
      if (formData.title !== originalRequest?.title) updateData.title = formData.title;
      if (formData.description !== originalRequest?.description) updateData.description = formData.description;
      if (formData.serviceType !== originalRequest?.serviceType) updateData.serviceType = formData.serviceType;
      if (formData.address !== originalRequest?.address) updateData.address = formData.address;
      if (formData.scheduledDate !== originalRequest?.scheduledDate) updateData.scheduledDate = formData.scheduledDate;
      if (formData.estimatedDuration !== originalRequest?.estimatedDuration?.toString()) {
        updateData.estimatedDuration = parseInt(formData.estimatedDuration);
      }
      if (formData.urgencyLevel !== originalRequest?.urgencyLevel) updateData.urgencyLevel = formData.urgencyLevel;
      if (formData.specialRequirements !== originalRequest?.specialRequirements) updateData.specialRequirements = formData.specialRequirements;
      if (formData.budget !== originalRequest?.budget?.toString()) {
        updateData.budget = parseFloat(formData.budget);
      }
      if (formData.contactPhone !== originalRequest?.contactPhone) updateData.contactPhone = formData.contactPhone;
      if (formData.notes !== originalRequest?.notes) updateData.notes = formData.notes;
      if (JSON.stringify(formData.images) !== JSON.stringify(originalRequest?.images)) {
        updateData.images = formData.images;
      }

      // Add coordinates if we have the original ones (for now, keep the same coordinates)
      if (originalRequest?.coordinates) {
        updateData.coordinates = originalRequest.coordinates;
      }

      console.log('🚀 Updating request with data:', updateData);

      if (Object.keys(updateData).length === 0) {
        setError('No changes detected');
        return;
      }

      const result = await apiService.updateRequest(id as string, updateData);
      console.log('✅ Request updated successfully:', result);

      setSuccess('Request updated successfully!');
      
      // Redirect to request details after a short delay
      setTimeout(() => {
        router.push(`/requests/${id}`);
      }, 2000);

    } catch (err: any) {
      console.error('❌ Error updating request:', err);
      
      let errorMessage = 'Failed to update request. Please try again.';
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading request details...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!user || user.role !== 'patient') {
    return (
      <Layout>
        <div className="text-center py-8">
          <p className="text-red-600">Only patients can edit requests.</p>
        </div>
      </Layout>
    );
  }

  if (error && !originalRequest) {
    return (
      <Layout>
        <div className="text-center py-8">
          <p className="text-red-600">{error}</p>
          <button
            type="button"
            onClick={() => router.back()}
            className="mt-4 text-blue-600 hover:text-blue-800"
          >
            Go Back
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Edit Request">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Edit Request</h1>
          <p className="text-gray-600">Update your nursing service request details</p>
        </div>

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <p className="text-green-600">{success}</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <Card className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Request Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Brief title for your request"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Type *
                </label>
                <select
                  name="serviceType"
                  value={formData.serviceType}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select service type</option>
                  <option value="wound_care">Wound Care</option>
                  <option value="medication_administration">Medication Administration</option>
                  <option value="post_surgical_care">Post-Surgical Care</option>
                  <option value="elderly_care">Elderly Care</option>
                  <option value="chronic_disease_management">Chronic Disease Management</option>
                  <option value="rehabilitation">Rehabilitation</option>
                  <option value="palliative_care">Palliative Care</option>
                  <option value="health_monitoring">Health Monitoring</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Detailed description of the care needed"
                required
              />
            </div>

            {/* Location and Schedule */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address *
                </label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Full address where service is needed"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Scheduled Date & Time *
                </label>
                <input
                  type="datetime-local"
                  name="scheduledDate"
                  value={formData.scheduledDate}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            {/* Duration and Urgency */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Duration (hours) *
                </label>
                <input
                  type="number"
                  name="estimatedDuration"
                  value={formData.estimatedDuration}
                  onChange={handleInputChange}
                  min="1"
                  max="24"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Urgency Level
                </label>
                <select
                  name="urgencyLevel"
                  value={formData.urgencyLevel}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Budget (EGP)
                </label>
                <input
                  type="number"
                  name="budget"
                  value={formData.budget}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Optional budget"
                />
              </div>
            </div>

            {/* Contact and Additional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Phone
                </label>
                <input
                  type="tel"
                  name="contactPhone"
                  value={formData.contactPhone}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Alternative contact number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Special Requirements
                </label>
                <input
                  type="text"
                  name="specialRequirements"
                  value={formData.specialRequirements}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Any special requirements"
                />
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Notes
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Any additional information for the nurse..."
              />
            </div>

            {/* Image Upload Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Medical Images (Optional)
              </label>
              <p className="text-sm text-gray-600 mb-3">
                Upload photos of the condition, medical reports, or any relevant images to help the nurse understand your needs better.
              </p>
              <ImageUpload
                onImagesChange={handleImagesChange}
                maxImages={5}
                maxSizePerImage={5}
                initialImages={formData.images}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-3 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {saving ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </div>
                ) : (
                  'Update Request'
                )}
              </button>
            </div>
          </form>
        </Card>
      </div>
    </Layout>
  );
}
