import { <PERSON>, Post, Get, Patch, Put, Body, Param, Query, UseGuards, Request, ValidationPipe } from '@nestjs/common';
import { RequestsService } from './requests.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateRequestDto, UpdateRequestDto, UpdateRequestStatusDto } from '../dto/request.dto';
import { RequestStatus } from '../schemas/patient-request.schema';

@Controller('api/requests')
@UseGuards(JwtAuthGuard)
export class RequestsController {
  constructor(private readonly requestsService: RequestsService) {}

  @Post()
  async createRequest(
    @Body(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      exceptionFactory: (errors) => {
        console.log('❌ Raw validation errors:', errors);
        const messages = errors.map(error => {
          console.log('❌ Error details:', {
            property: error.property,
            value: error.value,
            constraints: error.constraints,
            children: error.children
          });
          const constraints = error.constraints;
          return `${error.property}: ${constraints ? Object.values(constraints).join(', ') : 'Validation error'}`;
        });
        console.log('❌ Formatted validation errors:', messages);
        return new Error(`Validation failed: ${messages.join('; ')}`);
      }
    })) createRequestDto: CreateRequestDto,
    @Request() req : any
  ) {
    console.log('🔍 Controller createRequest called');
    console.log('🔍 req.user:', req.user);
    console.log('🔍 createRequestDto:', JSON.stringify(createRequestDto, null, 2));
    console.log('🔍 Images data:', createRequestDto.images);

    try {
      const result = await this.requestsService.createRequest(createRequestDto, req.user);
      console.log('✅ Request created successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ Create request failed:', error.message);
      console.error('❌ Error details:', error);
      throw error;
    }
  }

  @Get()
  async getRequests(@Request() req : any, @Query('status') status?: RequestStatus) {
    return this.requestsService.getRequests(req.user, status);
  }

  @Get('dashboard/stats')
  async getDashboardStats(@Request() req : any) {
    return this.requestsService.getDashboardStats(req.user);
  }

  @Patch(':id/status')
  async updateRequestStatus(
    @Param('id') requestId: string,
    @Body(ValidationPipe) updateStatusDto: UpdateRequestStatusDto,
    @Request() req : any
  ) {
    return this.requestsService.updateRequestStatus(requestId, updateStatusDto, req.user);
  }

  @Put(':id')
  async updateRequest(
    @Param('id') requestId: string,
    @Body(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      exceptionFactory: (errors) => {
        const messages = errors.map(error => {
          const constraints = error.constraints;
          return constraints ? Object.values(constraints).join(', ') : 'Validation error';
        });
        console.log('❌ Update validation errors:', messages);
        return new Error(`Validation failed: ${messages.join('; ')}`);
      }
    })) updateRequestDto: UpdateRequestDto,
    @Request() req : any
  ) {
    console.log('🎯 ===== UPDATE REQUEST ENDPOINT HIT =====');
    console.log('🔍 Controller updateRequest called');
    console.log('🔍 requestId:', requestId);
    console.log('🔍 updateRequestDto:', updateRequestDto);
    console.log('🔍 req.user:', req.user ? req.user._id : 'No user');
    console.log('🎯 ========================================');

    try {
      const result = await this.requestsService.updateRequest(requestId, updateRequestDto, req.user);
      console.log('✅ Update successful:', result);
      return result;
    } catch (error) {
      console.error('❌ Update failed:', error.message);
      throw error;
    }
  }

  @Get(':id')
  async getRequestById(@Param('id') requestId: string, @Request() req : any) {
    return this.requestsService.getRequestById(requestId, req.user);
  }
}
