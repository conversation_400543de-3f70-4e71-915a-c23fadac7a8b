const fetch = require('node-fetch');

async function testDebugEndpoint() {
  try {
    console.log('🐛 Testing Debug Endpoint...');
    
    // Test with the exact data structure that the frontend sends
    const frontendData = {
      title: 'Test Request with Images',
      description: 'This is a test request with image attachments for debugging validation issues',
      serviceType: 'wound_care',
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      estimatedDuration: 2,
      urgencyLevel: 'medium',
      specialRequirements: '',
      budget: 150,
      contactPhone: '01234567890',
      notes: 'Test notes for debugging',
      coordinates: [31.233, 30.033],
      images: [
        {
          filename: 'test-image-1.jpg',
          originalName: 'medical-report.jpg',
          url: '/api/uploads/images/test-image-1.jpg',
          size: 1024000
        },
        {
          filename: 'test-image-2.png',
          originalName: 'condition-photo.png',
          url: '/api/uploads/images/test-image-2.png',
          size: 2048000
        }
      ]
    };
    
    console.log('📡 Sending to debug endpoint...');
    console.log('🔍 Data structure:', JSON.stringify(frontendData, null, 2));
    
    const response = await fetch('http://localhost:3001/api/requests/debug', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(frontendData)
    });
    
    console.log('📊 Debug Response Status:', response.status);
    
    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Debug endpoint successful:', responseData);
    } else {
      const errorText = await response.text();
      console.error('❌ Debug endpoint failed:', errorText);
    }
    
    // Now try the same data with the real endpoint
    console.log('\n🔄 Now testing with real validation endpoint...');
    
    const realResponse = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(frontendData)
    });
    
    console.log('📊 Real Response Status:', realResponse.status);
    
    if (realResponse.ok) {
      const realResponseData = await realResponse.json();
      console.log('✅ Real endpoint successful:', realResponseData);
    } else {
      const realErrorText = await realResponse.text();
      console.error('❌ Real endpoint failed:', realErrorText);
      
      // Try to parse the error
      try {
        const errorJson = JSON.parse(realErrorText);
        console.error('❌ Parsed error details:', errorJson);
      } catch (e) {
        console.error('❌ Could not parse error as JSON');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('❌ Full error:', error);
  }
}

async function testWithEmptyImages() {
  try {
    console.log('\n🧪 Testing with Empty Images Array...');
    
    const dataWithEmptyImages = {
      title: 'Test Request with Empty Images',
      description: 'This is a test request with empty images array',
      serviceType: 'wound_care',
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      estimatedDuration: 2,
      urgencyLevel: 'medium',
      coordinates: [31.233, 30.033],
      images: [] // Empty array
    };
    
    console.log('📡 Testing with empty images array...');
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(dataWithEmptyImages)
    });
    
    console.log('📊 Empty Images Response Status:', response.status);
    
    if (response.ok) {
      console.log('✅ Empty images array works');
    } else {
      const errorText = await response.text();
      console.error('❌ Empty images array failed:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Empty images test failed:', error.message);
  }
}

async function testWithoutImages() {
  try {
    console.log('\n🧪 Testing without Images Field...');
    
    const dataWithoutImages = {
      title: 'Test Request without Images Field',
      description: 'This is a test request without images field at all',
      serviceType: 'wound_care',
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      estimatedDuration: 2,
      urgencyLevel: 'medium',
      coordinates: [31.233, 30.033]
      // No images field at all
    };
    
    console.log('📡 Testing without images field...');
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(dataWithoutImages)
    });
    
    console.log('📊 No Images Field Response Status:', response.status);
    
    if (response.ok) {
      console.log('✅ No images field works');
    } else {
      const errorText = await response.text();
      console.error('❌ No images field failed:', errorText);
    }
    
  } catch (error) {
    console.error('❌ No images field test failed:', error.message);
  }
}

// Run all tests
async function runTests() {
  await testDebugEndpoint();
  console.log('\n' + '='.repeat(50));
  await testWithEmptyImages();
  console.log('\n' + '='.repeat(50));
  await testWithoutImages();
}

runTests();
