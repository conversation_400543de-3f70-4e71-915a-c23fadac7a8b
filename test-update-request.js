const fetch = require('node-fetch');

async function testUpdateRequest() {
  try {
    console.log('🧪 Testing Update Request Endpoint...');
    
    // Test data
    const requestId = '688488db2d3294a8740257a7'; // Use the ID from your error
    const updateData = {
      title: 'Updated Test Request',
      description: 'This is an updated test description for the nursing request'
    };
    
    console.log('📡 Testing update request endpoint...');
    console.log('🔍 Request ID:', requestId);
    console.log('🔍 Update Data:', updateData);
    
    const response = await fetch(`http://localhost:3001/api/requests/${requestId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need a real token
      },
      body: JSON.stringify(updateData)
    });
    
    console.log('📊 Response Status:', response.status);
    console.log('📊 Response Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Update successful:', responseData);
    } else {
      const errorText = await response.text();
      console.error('❌ Update error:', errorText);
      
      // Try to parse as JSON if possible
      try {
        const errorJson = JSON.parse(errorText);
        console.error('❌ Error details:', errorJson);
      } catch (e) {
        console.error('❌ Raw error text:', errorText);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('❌ Full error:', error);
  }
}

// Also test if the endpoint exists at all
async function testEndpointExists() {
  try {
    console.log('🔍 Testing if endpoint exists...');
    
    const response = await fetch('http://localhost:3001/api/requests/test-id', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ test: 'data' })
    });
    
    console.log('📊 Endpoint test status:', response.status);
    
    if (response.status === 404) {
      console.error('❌ Endpoint not found - route may not be registered');
    } else if (response.status === 401) {
      console.log('✅ Endpoint exists but requires authentication');
    } else {
      console.log('✅ Endpoint exists');
    }
    
  } catch (error) {
    console.error('❌ Endpoint test failed:', error.message);
  }
}

// Run tests
async function runTests() {
  await testEndpointExists();
  console.log('\n' + '='.repeat(50) + '\n');
  await testUpdateRequest();
}

runTests();
