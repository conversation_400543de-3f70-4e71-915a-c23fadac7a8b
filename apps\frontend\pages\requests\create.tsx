import { useState } from 'react';
import { useAuth } from '../../lib/auth';
import Layout, { Card } from '../../components/Layout';
import { apiService } from '../../lib/api';
import { useRouter } from 'next/router';
import ImageUpload from '../../components/ImageUpload';

const SERVICE_TYPES = [
  { value: 'home_care', label: 'Home Care' },
  { value: 'medication_administration', label: 'Medication Administration' },
  { value: 'wound_care', label: 'Wound Care' },
  { value: 'vital_signs_monitoring', label: 'Vital Signs Monitoring' },
  { value: 'post_surgical_care', label: 'Post-Surgical Care' },
  { value: 'elderly_care', label: 'Elderly Care' },
  { value: 'pediatric_care', label: 'Pediatric Care' },
  { value: 'chronic_disease_management', label: 'Chronic Disease Management' },
];

const URGENCY_LEVELS = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'critical', label: 'Critical' },
];

export default function CreateRequest() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    serviceType: '',
    address: '',
    scheduledDate: '',
    estimatedDuration: '',
    urgencyLevel: 'medium',
    specialRequirements: '',
    budget: '',
    contactPhone: '',
    notes: '',
    images: [] as Array<{
      filename: string;
      originalName: string;
      url: string;
      size: number;
    }>,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImagesChange = (images: Array<{
    filename: string;
    originalName: string;
    url: string;
    size: number;
  }>) => {
    setFormData(prev => ({ ...prev, images }));
  };

  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.title.trim()) errors.push('Request title is required');
    if (formData.title.length < 5) errors.push('Title must be at least 5 characters');
    if (!formData.description.trim()) errors.push('Description is required');
    if (formData.description.length < 10) errors.push('Description must be at least 10 characters');
    if (!formData.serviceType) errors.push('Service type is required');
    if (!formData.address.trim()) errors.push('Address is required');
    if (!formData.scheduledDate) errors.push('Scheduled date is required');
    if (!formData.estimatedDuration) errors.push('Estimated duration is required');

    // Check if scheduled date is in the future
    const scheduledDateTime = new Date(formData.scheduledDate);
    if (scheduledDateTime <= new Date()) {
      errors.push('Scheduled date must be in the future');
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate form
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors.join('. '));
      return;
    }

    setLoading(true);

    try {
      // Get user's current location (simplified - in real app, use geolocation API)
      const coordinates = user?.location?.coordinates || [31.233, 30.033];

      const requestData = {
        ...formData,
        coordinates,
        scheduledDate: new Date(formData.scheduledDate).toISOString(),
        estimatedDuration: parseInt(formData.estimatedDuration) || 1,
        budget: formData.budget ? parseFloat(formData.budget) : undefined,
        contactPhone: formData.contactPhone.trim() || undefined, // Only include if not empty
      };

      console.log('🚀 Submitting request:', requestData);

      // Validate data before sending
      console.log('🔍 Validating request data:');
      console.log('- Title length:', requestData.title?.length);
      console.log('- Description length:', requestData.description?.length);
      console.log('- Coordinates:', requestData.coordinates);
      console.log('- Service type:', requestData.serviceType);
      console.log('- Scheduled date:', requestData.scheduledDate);
      console.log('- Contact phone:', requestData.contactPhone);
      console.log('- Images:', requestData.images);
      console.log('- Images count:', requestData.images?.length);
      console.log('- Images structure:', JSON.stringify(requestData.images, null, 2));

      const result = await apiService.createRequest(requestData);
      console.log('✅ Request created successfully:', result);

      // Extract request ID from response (handle double-nested structure)
      let requestId: string | undefined;
      if (result && typeof result === 'object') {
        const resultObj = result as any;
        requestId = resultObj?.data?.id || resultObj?.id || resultObj?._id;
        if (resultObj?.data?.data?.id) {
          requestId = resultObj.data.data.id;
        }
      }

      if (requestId) {
        // Show brief success message before redirect
        setError(''); // Clear any previous errors
        setSuccess(true);

        // Brief delay to show success state, then redirect
        setTimeout(() => {
          router.push(`/requests/success?id=${requestId}`);
        }, 1000);
      } else {
        // Fallback if no ID is returned
        console.warn('No request ID returned, redirecting to requests list');
        setSuccess(true);
        setTimeout(() => {
          router.push('/requests');
        }, 1000);
      }
    } catch (err: any) {
      console.error('❌ Error creating request:', err);
      console.error('❌ Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });

      // Extract detailed error message
      let errorMessage = 'Failed to create request. Please try again.';
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (!user || user.role !== 'patient') {
    return (
      <Layout>
        <div className="text-center py-8">
          <p className="text-red-600">Only patients can create service requests.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Create Service Request">
      <Card className="max-w-4xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Create Service Request</h1>
          <p className="text-gray-600">Fill out the form below to request nursing services.</p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Request Title *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Home nursing care needed"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Type *
              </label>
              <select
                name="serviceType"
                value={formData.serviceType}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select service type</option>
                {SERVICE_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              required
              rows={4}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe the care needed in detail..."
            />
          </div>

          {/* Location and Scheduling */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address *
              </label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Full address where service is needed"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Scheduled Date & Time *
              </label>
              <input
                type="datetime-local"
                name="scheduledDate"
                value={formData.scheduledDate}
                onChange={handleInputChange}
                required
                min={new Date(Date.now() + 60 * 60 * 1000).toISOString().slice(0, 16)} // Minimum 1 hour from now
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estimated Duration (hours) *
              </label>
              <input
                type="number"
                name="estimatedDuration"
                value={formData.estimatedDuration}
                onChange={handleInputChange}
                required
                min="1"
                max="24"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Urgency Level *
              </label>
              <select
                name="urgencyLevel"
                value={formData.urgencyLevel}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {URGENCY_LEVELS.map(level => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Budget (EGP) *
              </label>
              <input
                type="number"
                name="budget"
                value={formData.budget}
                onChange={handleInputChange}
                required
                min="0"
                step="0.01"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contact Phone (Optional)
              </label>
              <input
                type="tel"
                name="contactPhone"
                value={formData.contactPhone}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="01030321695"
                pattern="(\+20|0)?1[0-2,5]\d{8}"
                title="Please enter a valid Egyptian phone number (e.g., 01030321695) or leave empty"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Special Requirements
              </label>
              <input
                type="text"
                name="specialRequirements"
                value={formData.specialRequirements}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Experience with elderly patients"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Any additional information for the nurse..."
            />
          </div>

          {/* Image Upload Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Medical Images (Optional)
            </label>
            <p className="text-sm text-gray-600 mb-3">
              Upload photos of the condition, medical reports, or any relevant images to help the nurse understand your needs better.
            </p>
            <ImageUpload
              onImagesChange={handleImagesChange}
              maxImages={5}
              maxSizePerImage={5}
              initialImages={formData.images}
            />
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || success}
              className={`px-6 py-2 rounded-md text-white font-medium ${
                success
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'bg-blue-600 hover:bg-blue-700'
              } disabled:opacity-50 flex items-center`}
            >
              {success ? (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Request Created!
                </>
              ) : loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating...
                </>
              ) : (
                'Create Request'
              )}
            </button>
          </div>
        </form>
      </Card>
    </Layout>
  );
}
