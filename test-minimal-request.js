const fetch = require('node-fetch');

async function testMinimalRequest() {
  try {
    console.log('🧪 Testing Minimal Request Creation...');
    
    // Absolute minimal data that should pass validation
    const minimalData = {
      title: 'Minimal Test Request',
      description: 'This is a minimal test description that meets the 10 character requirement',
      serviceType: 'wound_care',
      coordinates: [31.233, 30.033],
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
    
    console.log('📡 Sending minimal request...');
    console.log('🔍 Data:', JSON.stringify(minimalData, null, 2));
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need a real token
      },
      body: JSON.stringify(minimalData)
    });
    
    console.log('📊 Response Status:', response.status);
    console.log('📊 Response Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Minimal request created successfully:', responseData);
    } else {
      const errorText = await response.text();
      console.error('❌ Minimal request failed:', errorText);
      
      // Try to parse as JSON
      try {
        const errorJson = JSON.parse(errorText);
        console.error('❌ Parsed error:', errorJson);
      } catch (e) {
        console.error('❌ Raw error text:', errorText);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testWithImages() {
  try {
    console.log('\n🧪 Testing Request with Images...');
    
    const dataWithImages = {
      title: 'Test Request with Images',
      description: 'This is a test description with images attached',
      serviceType: 'wound_care',
      coordinates: [31.233, 30.033],
      address: '123 Test Street, Cairo, Egypt',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      images: [
        {
          filename: 'test-image.jpg',
          originalName: 'photo.jpg',
          url: '/api/uploads/images/test-image.jpg',
          size: 1024000
        }
      ]
    };
    
    console.log('📡 Sending request with images...');
    console.log('🔍 Data:', JSON.stringify(dataWithImages, null, 2));
    
    const response = await fetch('http://localhost:3001/api/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(dataWithImages)
    });
    
    console.log('📊 Response Status:', response.status);
    
    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Request with images created successfully');
    } else {
      const errorText = await response.text();
      console.error('❌ Request with images failed:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Images test failed:', error.message);
  }
}

async function testFieldByField() {
  console.log('\n🧪 Testing Field Validation One by One...');
  
  const baseData = {
    title: 'Test',
    description: 'Test desc',
    serviceType: 'wound_care',
    coordinates: [31.233, 30.033],
    address: '123 Test St',
    scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  };
  
  // Test each field that might cause issues
  const testCases = [
    {
      name: 'Short title (should fail)',
      data: { ...baseData, title: 'Hi' }
    },
    {
      name: 'Short description (should fail)',
      data: { ...baseData, description: 'Short' }
    },
    {
      name: 'Invalid coordinates (should fail)',
      data: { ...baseData, coordinates: [31.233] }
    },
    {
      name: 'Invalid service type (should fail)',
      data: { ...baseData, serviceType: 'invalid_type' }
    },
    {
      name: 'Valid minimal data (should pass)',
      data: {
        title: 'Valid Test Request Title',
        description: 'This is a valid test description that meets all requirements',
        serviceType: 'wound_care',
        coordinates: [31.233, 30.033],
        address: '123 Valid Test Street, Cairo, Egypt',
        scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    
    try {
      const response = await fetch('http://localhost:3001/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        },
        body: JSON.stringify(testCase.data)
      });
      
      if (response.ok) {
        console.log(`✅ ${testCase.name}: PASSED`);
      } else {
        const errorText = await response.text();
        console.log(`❌ ${testCase.name}: FAILED - ${errorText.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ ${testCase.name}: ERROR - ${error.message}`);
    }
  }
}

// Run all tests
async function runTests() {
  await testMinimalRequest();
  console.log('\n' + '='.repeat(50));
  await testWithImages();
  console.log('\n' + '='.repeat(50));
  await testFieldByField();
}

runTests();
